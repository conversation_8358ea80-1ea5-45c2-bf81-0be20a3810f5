//! 同步状态
//! 
//! 该模块负责同步状态，包括检查池子的fee和同步池子
//! 
//! 主要函数:
//! - sync: 同步所有pool的数据
//! - check_pools_fee: 检查池子的fee
//! - process_fee_results: 处理fee结果
//! - process_non_stable_pools: 处理非稳定币池子

use std::sync::Arc;

use alloy::primitives::{Address, U256};
use alloy::{eips::BlockId, providers::Provider};
use futures::stream::{BufferUnordered, StreamExt};
use futures::stream::iter;
use colored::Colorize;
use std::collections::HashSet;
use std::sync::atomic::{AtomicUsize, Ordering};

use crate::vira::status::mev::MevStatus;
use crate::vira::status::pools::Pools;
use crate::CONFIG;
use crate::{connector::Connector, vira::{consts::{BATCH_SIZE, U_2000, U_666666, U_900004, U_900005, U_ZERO, MEV_BATCH_SIZE}, contract::{Contract, ViraData, ViraLogic}, dex::{factory::{DexFactory, FACTORY}}, errors::DEXError, pool::{DexPool, Status, POOL}, status::mev::{Mev, MevPool}}};

// 类型别名，简化使用
type PoolReq = ViraData::PoolReq;

use super::StatusManager;

// 并发限制常量
const MAX_CONCURRENT_TASKS: usize = 20;


//核心逻辑: 同步最新的pool addr并且更新所需data
pub async fn sync_new_pools(sm :&mut StatusManager, connector : Arc<Connector>) -> Result<(), DEXError> {
    let chain_tip = BlockId::from(connector.provider.get_block_number().await?);
    let factories = sm.factories.clone();
    
    // 使用iter和buffer_unordered替代FuturesUnordered
    let futures = iter(factories.into_iter().map(|(_, mut factory)| {
        let co = connector.clone();
        async move {
            let mut discovered_amms = factory.discover(chain_tip, co.clone()).await?;

            discovered_amms = factory.sync(discovered_amms, chain_tip, co).await?;

            Ok::<(Vec<POOL>, FACTORY), DEXError>((discovered_amms, factory))
        }
    }))
    .buffer_unordered(MAX_CONCURRENT_TASKS);

    let mut new_pools = Vec::new();
    futures::pin_mut!(futures);
    while let Some(res) = futures.next().await {
        let (synced_amms, factory) = res?;
        new_pools.extend(synced_amms);
        sm.factories.insert(factory.address(), factory); //更新factory的索引到当前更新到的位置
    }

    check_pools_fee(new_pools, sm, connector.clone()).await?;

    Ok(())
}


/// 检查池子的fee
/// 
/// 该函数接收一个池子列表、状态管理器和连接器，
/// 并对每个池子进行费用检查。检查的结果将更新到pools并且添加到sm中
/// 
/// 参数:
/// - `pools`: 需要检查费用的池子列表
/// - `sm`: 状态管理器，用于管理池子的状态
/// - `connector`: 连接器，用于与区块链进行交互
/// 
/// 返回:
/// - `Result<(), DEXError>`: 成功时返回 Ok，失败时返回 DEXError
/// 
/// 核心逻辑:
/// 1. 遍历pools，把包含stable的pools分成一组，每BATCH_SIZE数量的pools使用contract.batch_check_pair_fee检查fee, 根据返回的结果设置pool的状态
///    - 默认状态为Status::Good
///         1) fee == 900004 (通缩币) Status::OddBad;
///         2) fee == 900005 (未知状态) Status::Unknown;
///         3) fee > 2000 Status::Bad;
///         4) 0 < fee < 2000 Status::Fee;
///         5) fee == 666666 (蜜罐币) Status::OddGood;
///         6) 其余情况保持Good状态
/// 2. 把OddGood和Good的pool增加到sm中
/// 3. 检查剩余的uncheck pool, 逻辑参考 checkpoint::process_non_stable_pools
/// 4. 把剩余pools中状态为OddGood和Good的pool增加到sm中
/// 5. 重复一次步骤3和4
/// 6. 动态打印结果

pub async fn check_pools_fee(pools: Vec<POOL>, sm: &mut StatusManager, connector: Arc<Connector>) -> Result<(), DEXError> {
    println!("{}", "\nChecking pools fee...".green());

    // 创建合约实例
    let contract = Arc::new(Contract::new(CONFIG.contract, connector.clone()));

    // 统计总池子数量
    let total_pools = pools.len();
    println!("Total pools to check: {}", total_pools);

    // 将池子分成两组：包含稳定币和不包含稳定币的
    let stables: HashSet<_> = CONFIG.stables.iter().cloned().collect();
    
    let (stable_pools, non_stable_pools): (Vec<_>, Vec<_>) = pools
        .into_iter()
        .partition(|pool| {
            pool.data().tokens.iter().any(|token| {
                stables.contains(&token.addr)
            })
        });
    
    let stable_len = stable_pools.len();
    let non_stable_len = non_stable_pools.len();
    
    println!("Stable pools: {}, Non-stable pools: {}", 
        stable_len, 
        non_stable_len
    );

    // 处理所有池子的进度计数器
    let processed = Arc::new(AtomicUsize::new(0));

    // 1. 处理包含稳定币的池子
    let processed_stable_pools = process_stable_pools(stable_pools, sm, contract.clone(), processed.clone()).await?;
    
    // 2. 处理不包含稳定币的池子
    let processed_non_stable_pools = process_non_stable_pools_concurrent(
        non_stable_pools, 
        &contract, 
        sm, 
        processed.clone(),
        stable_len, // 开始索引从stable_pools.len()开始
        &stables
    ).await?;

    println!("\nPool fee checking completed! Total processed: {}", 
        processed_stable_pools + processed_non_stable_pools);
    
    Ok(())
}

/// 处理包含稳定币的池子，并发处理多个批次
async fn process_stable_pools(
    stable_pools: Vec<POOL>, 
    sm: &mut StatusManager, 
    contract: Arc<Contract>,
    processed_counter: Arc<AtomicUsize>
) -> Result<usize, DEXError> {
    if stable_pools.is_empty() {
        return Ok(0);
    }

    println!("\nProcessing stable pools...");
    let total = stable_pools.len();
    let chunks: Vec<Vec<POOL>> = stable_pools
        .chunks(BATCH_SIZE)
        .map(|chunk| chunk.to_vec())
        .collect();
    
    // 重置处理计数器确保百分比计算正确
    processed_counter.store(0, Ordering::SeqCst);
    
    // 使用iter和buffer_unordered替代FuturesUnordered
    let futures = iter(chunks.into_iter().enumerate().map(|(_, chunk)| {
        let contract_clone = contract.clone();
        let processed_counter_clone = processed_counter.clone();
        
        async move {
            let pool_reqs = prepare_stable_pool_requests(&chunk);
            
            if pool_reqs.is_empty() {
                return Ok::<(Vec<POOL>, Vec<Vec<alloy::primitives::U256>>), DEXError>((chunk, vec![]));
            }
            
            match contract_clone.batch_check_pair_fee(pool_reqs).await {
                Ok(fees) => {
                    let current = processed_counter_clone.fetch_add(chunk.len(), Ordering::SeqCst);
                    eprint!("\r处理稳定币池子进度: {}/{} ({:.2}%)", 
                        current + chunk.len(), 
                        total,
                        ((current + chunk.len()) as f64 / total as f64) * 100.0
                    );
                    
                    Ok((chunk, fees))
                },
                Err(e) => Err(e)
            }
        }
    }))
    .buffer_unordered(MAX_CONCURRENT_TASKS);
    
    let mut processed_pools = Vec::new();
    let mut processed_count = 0;
    
    // 收集并处理结果
    futures::pin_mut!(futures);
    while let Some(result) = futures.next().await {
        match result {
            Ok((chunk, fees)) => {
                if !fees.is_empty() {
                    let mut chunk_copy = chunk.clone();
                    process_fee_results(&mut chunk_copy, &fees, sm, processed_count);
                    processed_count += chunk.len();
                    processed_pools.extend(chunk);
                }
            },
            Err(e) => {
                println!("\n{}", format!("Error processing stable pools batch: {:?}", e).red());
                return Err(e);
            }
        }
    }
    
    println!("\n稳定币池子处理完成: 总共处理 {}/{} ({:.2}%)",
        processed_count, 
        total,
        (processed_count as f64 / total as f64) * 100.0
    );
    
    Ok(processed_count)
}

/// 准备稳定币池子的请求
fn prepare_stable_pool_requests(pools: &[POOL]) -> Vec<ViraLogic::CheckPairFeeInputDesc> {
    let stables: HashSet<_> = CONFIG.stables.iter().cloned().collect();
    let mut pool_reqs = Vec::with_capacity(pools.len());
    
    for pool in pools {
        let data = pool.data();
        // 找到稳定币的索引
        let stable_index = data.tokens.iter()
            .position(|t| stables.contains(&t.addr))
            .unwrap_or(0);
            
        // 构建请求
        pool_reqs.push(ViraLogic::CheckPairFeeInputDesc {
            prePair: vec![],
            pair: vec![PoolReq {
                addr: data.addr,
                version: U256::from(data.ver),
                fee: U_ZERO,
                fp: data.fp,
                inIndex: U256::from(stable_index),
                outIndex: U_ZERO,
            }],
        });
    }
    
    pool_reqs
}

/// 并发处理非稳定币池子
async fn process_non_stable_pools_concurrent(
    non_stable_pools: Vec<POOL>, 
    contract: &Arc<Contract>, 
    sm: &mut StatusManager,
    processed_counter: Arc<AtomicUsize>,
    start_index: usize,
    stables: &HashSet<alloy::primitives::Address>
) -> Result<usize, DEXError> {
    if non_stable_pools.is_empty() {
        return Ok(0);
    }

    println!("\nProcessing non-stable pools (1st round)...");
    
    // 第一轮处理
    let (processed_first, remaining_pools) = process_non_stable_pools_round(
        non_stable_pools,
        contract,
        sm,
        processed_counter.clone(),
        start_index,
        stables,
        false
    ).await?;
        
    if remaining_pools.is_empty() {
        return Ok(processed_first);
    }
    
    // 第二轮处理
    println!("\nProcessing non-stable pools (2nd round)...");
    let (processed_second, _) = process_non_stable_pools_round(
        remaining_pools,
        contract,
        sm,
        processed_counter,
        start_index + processed_first,
        stables,
        true
    ).await?;
    
    let final_count = processed_first + processed_second;
    // (暂时去除)标记剩余未检查的池子为NoStable, 有用的pool都已经添加到sm当中
    /*
    for mut pool in remaining_second {
        if pool.data().status == Status::UnChecked {
            pool.data_mut().status = Status::NoStable;
            final_count += 1;
        }
    }
    */
    Ok(final_count)
}

/// 并发处理非稳定币池子的单轮
async fn process_non_stable_pools_round(
    pools: Vec<POOL>,
    contract: &Arc<Contract>,
    sm: &mut StatusManager,
    processed_counter: Arc<AtomicUsize>,
    start_index: usize,
    stables: &HashSet<Address>,
    is_second_round: bool
) -> Result<(usize, Vec<POOL>), DEXError> {
    let total = pools.len();
    if total == 0 {
        return Ok((0, vec![]));
    }
    
    let round_name = if is_second_round { "2nd round" } else { "1st round" };
    println!("Processing {} non-stable pools ({})", total, round_name);
    
    // 先预处理所有的请求，将pools分成两部分：有pre_pairs的和无pre_pairs的
    let mut all_requests = Vec::with_capacity(pools.len());
    let mut unchecked_pools = Vec::new();
    
    for pool in pools {
        let data = pool.data();
        let mut found_path = false;
        
        // 遍历pool中的每个token，寻找到stable的路径
        for (token_idx, token) in data.tokens.iter().enumerate() {
            let path = sm.index.get_path(&token.addr, stables, &sm.pools, None, None, false);

            // 如果找到路径，将所有池子作为pre_pair
            if !path.is_empty() {
                let pre_pairs: Vec<PoolReq> = path.iter()
                    .rev()  // 反转整个路径顺序
                    .map(|pool_index|
                        PoolReq {
                        addr: pool_index.addr,
                        version: U256::from(sm.pools.data.get(&pool_index.addr).unwrap().data().ver),
                        fee: U256::ZERO,
                        fp: sm.pools.data.get(&pool_index.addr).unwrap().data().fp,
                        inIndex: U256::from(pool_index.out_index),  // 反转 index
                        outIndex: U256::from(pool_index.in_index),  // 反转 index
                    })
                    .collect();
                
                // 创建当前池子的请求
                let pool_req = ViraLogic::CheckPairFeeInputDesc {
                    prePair: pre_pairs,
                    pair: vec![PoolReq {
                        addr: data.addr,
                        version: U256::from(data.ver),
                        fee: U_ZERO,
                        fp: data.fp,
                        inIndex: U256::from(token_idx),
                        outIndex: U_ZERO,
                    }],
                };
                
                all_requests.push((pool.clone(), pool_req));
                found_path = true;
                break;
            }
        }
        
        // 如果没有找到路径，直接将池子加入到unchecked_pools
        if !found_path {
            unchecked_pools.push(pool);
        }
    }
    
    println!("Found {} pools with path to stables, {} pools without path", 
        all_requests.len(), unchecked_pools.len());
    
    // 如果没有可处理的请求，直接返回
    if all_requests.is_empty() {
        return Ok((0, unchecked_pools));
    }
    
    // 重置处理计数器，确保百分比计算正确
    processed_counter.store(0, Ordering::SeqCst);
    let requests_total = all_requests.len();
    
    // 将请求分成更小的批次进行并发处理
    // 使用iter和buffer_unordered替代FuturesUnordered
    let futures = iter(all_requests.chunks(BATCH_SIZE).enumerate().map(|(batch_idx, batch)| {
        let mut valid_reqs = Vec::new();
        let mut pool_mapping = Vec::new();
        
        // 只处理有效的请求
        for (pool, req) in batch {
            valid_reqs.push(req.clone());
            pool_mapping.push(pool.clone());
        }

        let contract_clone = contract.clone();
        let processed_counter_clone = processed_counter.clone();
        let batch_start_idx = start_index + batch_idx * BATCH_SIZE;
        
        async move {
            match contract_clone.batch_check_pair_fee(valid_reqs).await {
                Ok(fees) => {
                    let current = processed_counter_clone.fetch_add(pool_mapping.len(), Ordering::SeqCst);
                    eprint!("\r处理非稳定币池子进度 ({}): {}/{} ({:.2}%)", 
                        round_name,
                        current + pool_mapping.len(), 
                        requests_total,
                        ((current + pool_mapping.len()) as f64 / requests_total as f64) * 100.0
                    );
                    
                    Ok((pool_mapping, fees, batch_start_idx))
                },
                Err(e) => {
                    panic!("{}", format!("Error checking fees for pools batch: {:?}", e).red());
                }
            }
        }
    }))
    .buffer_unordered(MAX_CONCURRENT_TASKS);
    
    let mut processed_count = 0;
    let mut processed_pools = Vec::new();
    
    // 收集并处理结果
    futures::pin_mut!(futures);
    while let Some(result) = futures.next().await {
        match result {
            Ok((pool_mapping, fees, batch_start_idx)) => {
                if !fees.is_empty() {
                    let mut pools_to_process = pool_mapping.clone();
                    process_fee_results(&mut pools_to_process, &fees, sm, batch_start_idx);
                    processed_count += fees.len();
                    processed_pools.extend(pool_mapping);
                }
            },
            Err(e) => {
                println!("\n{}", format!("Error processing non-stable pools batch: {:?}", e).red());
                return Err(e);
            }
        }
    }
    
    println!("\n非稳定币池子处理完成: 总共处理 {}/{} ({:.2}%)",
        processed_count, 
        requests_total,
        (processed_count as f64 / requests_total as f64) * 100.0
    );
    
    // 直接返回预先分离的未检查池子
    Ok((processed_count, unchecked_pools))
}

// 处理fee结果的辅助函数
fn process_fee_results(
    chunk: &mut [POOL], 
    fees: &[Vec<alloy::primitives::U256>], 
    sm: &mut StatusManager,
    start_index: usize
) {
    // 批量收集需要添加到 StatusManager 的池子
    let mut pools_to_add = Vec::new();

    for (i, (pool, fee)) in chunk.iter_mut().zip(fees).enumerate() {
        let data = pool.data_mut();
        
        // 先设置所有token的fee值
        let mut has_honey = false;
        for (token, &val) in data.tokens.iter_mut().zip(fee.iter()) {
            // 检查是否是蜜罐币
            if val == U_666666 {
                has_honey = true;
                token.fee = U_ZERO; // 蜜罐币的fee设为0
            } else {
                token.fee = val;
            }
        }

        // 默认状态为Good
        let mut current_status = Status::Good;
        
        // 按照优先级判断状态
        // 1. 检查是否存在900004 (通缩币)
        if data.tokens.iter().any(|t| t.fee == U_900004) {
            current_status = Status::OddBad;
        }
        // 2. 检查是否存在900005 (未知状态)
        else if data.tokens.iter().any(|t| t.fee == U_900005) {
            current_status = Status::Unknown;
        }
        // 3. 检查是否有fee大于2000
        else if data.tokens.iter().any(|t| t.fee > U_2000) {
            current_status = Status::Bad;
        }
        // 4. 检查是否有fee大于0小于2000
        else if data.tokens.iter().any(|t| t.fee > U_ZERO && t.fee <= U_2000) {
            current_status = Status::Fee;
        }
        // 5. 检查是否存在666666 (Honey pot)
        else if has_honey {
            current_status = Status::OddGood;
        }
        // 6. 其余情况保持Good状态
        
        // 设置状态
        data.status = current_status;

        // 打印结果
        let fee_info = data.tokens.iter()
            .map(|token| {
                if token.fee == U_ZERO {
                    token.symbol.to_string()
                } else {
                    format!("{} ({})", token.symbol, token.fee)
                }
            })
            .collect::<Vec<_>>()
            .join(" - ");

        let line = format!("({}) {} {}", start_index + i, data.addr, fee_info);
        let colored_line = match data.status {
            Status::Fee => line.green().to_string(),
            Status::Bad => line.red().to_string(),
            Status::OddGood => line.yellow().to_string(),
            Status::OddBad => line.red().bold().to_string(),
            Status::Unknown => line.bright_purple().to_string(),
            Status::NoStable => line.black().to_string(),
            Status::LowValue => line.bright_black().to_string(),
            _ => line
        };
        println!("{}", colored_line);

        // 将有效的pool添加到StatusManager中
        if data.status == Status::Good || data.status == Status::OddGood {
            pools_to_add.push(pool.clone());
        }
    }

    // 批量添加到 StatusManager
    for pool in pools_to_add {
        sm.add(pool);
    }
}


/// 检查MEV路径的费用和状态
///
/// 该函数实现MEV路径的批量检查，优化了逻辑结构和可读性
///
/// 核心逻辑：
/// 1. 收集所有需要检查的MEV池子（每个池子的所有MEV路径状态相同）
/// 2. 按批次大小分组进行合约调用
/// 3. 处理返回结果并更新MEV状态
/// 4. 实现错误处理和重试机制
///
/// # 参数
/// * `contract` - 合约实例，用于调用batch_check_mev
/// * `pools` - 池子数据，包含MEV路径信息
///
/// # 返回值
/// * `Result<(), DEXError>` - 成功时返回Ok，失败时返回错误
pub async fn check_mevs(contract: &Contract, pools: &Pools) -> Result<(), DEXError> {
    println!("{}, pools.data: {}, pools.mev: {}", "
开始检查MEV路径...".green(), pools.data.len(), pools.mevs.len());

    // 收集所有需要检查的MEV池子地址（每个池子的所有MEV路径状态相同）
    let mut unchecked_pool = Vec::new();
    for entry in pools.mevs.iter() {
        let pool_addr = *entry.key();
        let mev_list = entry.value();

        for (i, mev) in mev_list.iter().enumerate() {
            if mev.status == MevStatus::Unchecked {
                unchecked_pool.push((pool_addr, i));
            }
        }
    }

    if unchecked_pool.is_empty() {
        println!("没有需要检查的MEV路径");
        return Ok(());
    }

    // 统计总MEV数量用于进度显示
    let total_mevs: usize = unchecked_pool.len();

    println!("找到 {} 个池子，共 {} 个MEV路径需要检查", pools.data.len(), total_mevs);

    // 按批次处理MEV路径
    let mut processed_count = 0;
    let mut zero_fee_mev_count = 0;
    let mut non_zero_fee_mev_count = 0;

    // 将池子地址分批处理
    for batch_addrs in unchecked_pool.chunks(MEV_BATCH_SIZE) {
        match process_mev_batch_by_pools(contract, batch_addrs, pools).await {
            Ok((success_count, batch_zero_fee, batch_non_zero_fee)) => {
                processed_count += success_count;
                zero_fee_mev_count += batch_zero_fee;
                non_zero_fee_mev_count += batch_non_zero_fee;
                let percentage = (processed_count as f64 / total_mevs as f64) * 100.0;
                println!("已处理 {}/{} 个MEV路径 ({:.1}%)", processed_count, total_mevs, percentage);
            }
            Err(e) => {
                println!("批量处理MEV失败: {:?}", e);
                // 继续处理下一批，不中断整个流程
            }
        }
    }

    println!("{}", format!("MEV路径检查完成！总共处理了 {} 个MEV路径", processed_count).green());
    println!("  - MEV中所有fee都为0的数量: {}", zero_fee_mev_count);
    println!("  - MEV中存在fee大于0的数量: {}", non_zero_fee_mev_count);
    Ok(())
}

/// 处理一批池子的MEV路径检查
///
/// 将每个池子的所有MEV路径转换为合约请求格式，调用合约进行批量检查，并处理结果
///
/// # 参数
/// * `contract` - 合约实例
/// * `pool_addrs` - 需要检查的池子地址数组
/// * `pools` - 池子数据，用于获取池子详细信息
///
/// # 返回值
/// * `Result<usize, DEXError>` - 成功处理的MEV数量
async fn process_mev_batch_by_pools(
    contract: &Contract,
    pool_and_indexs: &[(Address, usize)],
    pools: &Pools,
) -> Result<(usize, usize, usize), DEXError> {
    // 构建合约请求数据 - 内联简短的构建逻辑
    let mut pool_reqs_batch = Vec::new();
    let mut mev_pool_mapping = Vec::new(); // 记录每个请求对应的池子地址，用于结果处理

    // 构建单个MEV的PoolReq数据
    for (addr, index) in pool_and_indexs {
        if let Some(mevs) = pools.mevs.get(addr) {
            let mev = &mevs[*index];
            let pool = pools.data.get(addr).unwrap();
            let data = pool.data();

            let mut mev_req = Vec::new();
            for p in &mev.pools {
                mev_req.push(ViraData::PoolReq {
                    addr: p.addr,
                    version: U256::from(data.ver),
                    fee: U256::ZERO,  // 费用在合约中计算
                    fp: data.fp,
                    inIndex: U256::from(p.in_index),
                    outIndex: U256::from(p.out_index),
                });
            }

            pool_reqs_batch.push(mev_req);
            mev_pool_mapping.push((addr, index));
        }
    }


    if pool_reqs_batch.is_empty() {
        println!("警告：没有有效的MEV路径数据可供检查");
        return Ok((0, 0, 0));
    }

    println!("正在检查 {} 个MEV路径...", pool_reqs_batch.len());

    // 尝试批量检查，失败时进行单个重试 - 内联重试逻辑
    let check_results = match contract.batch_check_mev(pool_reqs_batch.clone()).await {
        Ok(results) => results,
        Err(_) => {
            println!("{}批量合约调用失败，开始单个重试...", "⚠️ ".yellow());

            // 等待2秒后重试
            tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

            // 单个重试逻辑
            let mut all_results = Vec::new();
            for single_req in pool_reqs_batch {
                match contract.batch_check_mev(vec![single_req]).await {
                    Ok(mut results) => {
                        if !results.is_empty() {
                            all_results.push(results.remove(0));
                        } else {
                            // 为失败的MEV创建一个空结果
                            all_results.push(ViraLogic::CheckMevsResultDesc {
                                gas: U256::ZERO,
                                fee0: vec![],
                                fee1: vec![],
                            });
                        }
                    }
                    Err(_) => {
                        // 为失败的MEV创建一个空结果
                        all_results.push(ViraLogic::CheckMevsResultDesc {
                            gas: U256::ZERO,
                            fee0: vec![],
                            fee1: vec![],
                        });
                    }
                }
            }
            all_results
        }
    };

    let mut zero_fee_mev_count = 0;
    let mut non_zero_fee_mev_count = 0;
    for result in &check_results {
        let has_non_zero_fee = result.fee0.iter().any(|&f| f > U256::ZERO) || result.fee1.iter().any(|&f| f > U256::ZERO);
        if has_non_zero_fee {
            non_zero_fee_mev_count += 1;
        } else {
            zero_fee_mev_count += 1;
        }
    }

    // 处理检查结果并更新MEV状态 - 修复费用写入逻辑
    let mut processed_count = 0;
    for (i, result) in check_results.iter().enumerate() {
        if i >= mev_pool_mapping.len() {
            break;
        }

        let (addr, index) = mev_pool_mapping[i];

        // 更新对应池子的所有MEV状态（因为同一个池子的所有MEV状态相同）
        if let Some(mut mev_list) = pools.mevs.get_mut(addr) {
            let mev = &mut mev_list[*index];
            // 更新gas消耗
            mev.gas = result.gas;

            // 更新正向交易状态 (status0) 和费用
            mev.status = if !result.fee0.is_empty() {
                update_mev_pool_fees(&mut mev.pools, &result.fee0, true);
                MevStatus::Active
            } else {
                // 如果费用数组为空，清空所有池子的fee0
                for pool in &mut mev.pools {
                    pool.fee = U256::ZERO;
                }
                MevStatus::Bad
            };

            // 更新反向交易状态 (status1) 和费用
            mev.status_desc = if !result.fee1.is_empty() {
                update_mev_pool_fees(&mut mev.pools, &result.fee1, false);
                MevStatus::Active
            } else {
                // 如果费用数组为空，清空所有池子的fee1
                for pool in &mut mev.pools {
                    pool.fee_desc = U256::ZERO;
                }
                MevStatus::Bad
            };

            processed_count += 1;
            
        }
    }

    Ok((processed_count, zero_fee_mev_count, non_zero_fee_mev_count))
}

/// 更新MEV路径中各个池子的费用
///
/// 将合约返回的费用数组按顺序写入到MEV路径中对应池子的费用字段
///
/// # 参数
/// * `mev_pools` - MEV路径中的池子列表
/// * `fee_array` - 合约返回的费用数组
/// * `order_asc` - true表示顺序，false逆向
fn update_mev_pool_fees(mev_pools: &mut [MevPool], fee_array: &[U256], order_asc: bool) {
    // 检查数组长度是否匹配
    if fee_array.len() != mev_pools.len() {
        panic!(
            "⚠️ 费用数组长度不匹配: 期望 {} 个费用，实际收到 {} 个费用",
            mev_pools.len(),
            fee_array.len()
        );
    } else {
        // 长度匹配的正常情况：按顺序更新每个池子的费用
        for (i, fee) in fee_array.iter().enumerate() {
            if order_asc {
                mev_pools[i].fee = *fee;
            } else {
                mev_pools[i].fee_desc = *fee;
            }
        }
    }
}


#[cfg(test)]
mod tests {
    use super::*;
    use crate::vira::status::mev::{Mev, MevPool};
    use crate::vira::pool::{PoolData, PoolDataToken};
    use std::str::FromStr;

    /// 创建测试用的池子数据
    fn create_test_pool(addr: &str, ver: u16) -> POOL {
        let mut pool_data = PoolData::default();
        pool_data.addr = Address::from_str(addr).unwrap();
        pool_data.ver = ver;
        pool_data.fp = U256::from(3000); // 0.3% fee

        // 添加两个测试token
        pool_data.tokens = vec![
            PoolDataToken {
                addr: Address::from_str("0x1111111111111111111111111111111111111111").unwrap(),
                index: 0,
                symbol: "TOKEN0".to_string(),
                decimal: 18,
                reserve: U256::from(1000000),
                fee: U256::ZERO,
                weight: U256::ZERO,
            },
            PoolDataToken {
                addr: Address::from_str("0x2222222222222222222222222222222222222222").unwrap(),
                index: 1,
                symbol: "TOKEN1".to_string(),
                decimal: 18,
                reserve: U256::from(2000000),
                fee: U256::ZERO,
                weight: U256::ZERO,
            },
        ];

        // 创建UniV2Pool实例
        use crate::vira::dex::uni_v2::pool::UniV2Pool;
        POOL::UniV2Pool(UniV2Pool { data: pool_data })
    }

    /// 创建测试用的MEV路径
    fn create_test_mev(pool_addrs: Vec<&str>) -> Mev {
        let mut mev = Mev::default();
        mev.s_in = Address::from_str("0x1111111111111111111111111111111111111111").unwrap();
        mev.s_out = Address::from_str("0x2222222222222222222222222222222222222222").unwrap();

        mev.pools = pool_addrs.iter().enumerate().map(|(_i, addr)| {
            MevPool {
                addr: Address::from_str(addr).unwrap(),
                in_index: 0,
                out_index: 1,
                fee: U256::ZERO,
                fee_desc: U256::ZERO,
            }
        }).collect();

        mev
    }

    #[test]
    fn test_mev_data_structure() {
        // 测试MEV数据结构的创建和基本属性
        let mev = create_test_mev(vec![
            "0x3333333333333333333333333333333333333333",
            "0x4444444444444444444444444444444444444444"
        ]);

        assert_eq!(mev.pools.len(), 2);
        assert_eq!(mev.status, MevStatus::Unchecked);
        assert_eq!(mev.status_desc, MevStatus::Unchecked);
        println!("✅ MEV数据结构测试通过");
    }

    #[test]
    fn test_pools_data_structure() {
        // 测试Pools数据结构
        let pools = Pools::default();

        // 添加测试池子
        let pool1 = create_test_pool("0x3333333333333333333333333333333333333333", 2);
        let pool2 = create_test_pool("0x4444444444444444444444444444444444444444", 2);

        pools.data.insert(pool1.addr(), pool1);
        pools.data.insert(pool2.addr(), pool2);

        // 添加测试MEV
        let mev = create_test_mev(vec![
            "0x3333333333333333333333333333333333333333",
            "0x4444444444444444444444444444444444444444"
        ]);

        let main_pool_addr = Address::from_str("0x5555555555555555555555555555555555555555").unwrap();
        pools.mevs.insert(main_pool_addr, vec![mev]);

        assert_eq!(pools.data.len(), 2);
        assert_eq!(pools.mevs.len(), 1);
        println!("✅ Pools数据结构测试通过");
    }

    /// 模拟check_mevs函数的数据准备逻辑
    #[test]
    fn test_mev_filtering_logic() {
        let pools = Pools::default();

        // 创建混合状态的MEV路径
        let mut mev1 = create_test_mev(vec!["0x3333333333333333333333333333333333333333"]);
        mev1.status = MevStatus::Unchecked; // 需要检查
        mev1.status_desc = MevStatus::Active;    // 已检查

        let mut mev2 = create_test_mev(vec!["0x4444444444444444444444444444444444444444"]);
        mev2.status = MevStatus::Active;    // 已检查
        mev2.status_desc = MevStatus::Active;    // 已检查

        let mut mev3 = create_test_mev(vec!["0x5555555555555555555555555555555555555555"]);
        mev3.status = MevStatus::Unchecked; // 需要检查
        mev3.status_desc = MevStatus::Unchecked; // 需要检查

        let main_pool_addr = Address::from_str("0x6666666666666666666666666666666666666666").unwrap();
        pools.mevs.insert(main_pool_addr, vec![mev1, mev2, mev3]);

        // 模拟过滤逻辑
        let mut unchecked_count = 0;
        for entry in pools.mevs.iter() {
            let mev_list = entry.value();
            for mev in mev_list.iter() {
                if mev.status == MevStatus::Unchecked || mev.status_desc == MevStatus::Unchecked {
                    unchecked_count += 1;
                }
            }
        }

        assert_eq!(unchecked_count, 2); // mev1 和 mev3 需要检查
        println!("✅ MEV过滤逻辑测试通过，找到 {} 个需要检查的MEV", unchecked_count);
    }
}
